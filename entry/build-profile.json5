{
  apiType: 'stageMode',
  buildOption: {
    arkOptions: {
      runtimeOnly: {
        packages: [
          '@taobao-ohos/module',
          '@taobao-ohos/taobao_runtime',
          '@taobao-ohos/nav',
        ],
        sources: [
        ],
      },
    },
  },
  buildOptionSet: [
    {
      name: 'release',
      arkOptions: {
        obfuscation: {
          ruleOptions: {
            enable: true,
            files: [
              './obfuscation-rules.txt',
            ],
          },
        },
      },
    },
  ],
  targets: [
    {
      name: 'default',
    },
    {
      name: 'ohosTest',
    },
  ],
}