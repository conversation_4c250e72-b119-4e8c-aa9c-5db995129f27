import { Nav } from '@taobao-ohos/nav'
import uri from '@ohos.uri';

//
// test();

@Entry
@Component
struct Index {
  @State message: string = '点击跳转你的页面';

  build() {
    Row() {
      Column() {
        Button(this.message)
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .onClick(() => {
            Nav
              .from(this)
              .withExtras({ "bizData": this.message })
              .toUri(new uri.URI("https://m.taobao.com/demo"))
          })
      }
      .width('100%')
    }
    .height('100%')
  }
}