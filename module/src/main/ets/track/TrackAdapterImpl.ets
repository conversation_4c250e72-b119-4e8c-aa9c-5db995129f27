import { ITrackAdapter } from '@fliggy-ohos/unicorn';
import { PageContext } from '@fliggy-ohos/router';
import { tracker } from '@fliggy-ohos/tracker';
import { logger } from '../pages/FlutterPageController';

export class TrackAdapterImpl implements ITrackAdapter {
  pageContext: PageContext;
  private mWebHostContent = new Map<string, string>();

  constructor(pageContext: PageContext) {
    this.pageContext = pageContext;
  }

  onLoadUrl(pageUrl: string): void {

  }

  onPageFinish(): void {

  }

  onRecvMtop(): void {

  }

  setCacheType(cacheType: string): void {

  }

  callBridge(method: string, errorCode?: string | undefined, errorMsg?: string | undefined): void {

  }

  addJsError(url: string, errorCode: string, errorMsg: string): void {

  }

  addNetworkError(url: string, code: string): void {

  }

  getWebHostContent(key: string): string | undefined {
    return this.mWebHostContent.get(key);
  }

  setWebHostContent(key: string, value: string): void {
    this.mWebHostContent.set(key, value);
  }

  trackPageLeave(): void {
    try{
      if(this.mWebHostContent){
        tracker.updatePageProperties(this.pageContext, this.mWebHostContent);

        if(this.pageContext.params["ut-map"]) {
          let utMap: object = this.pageContext.params["ut-map"];
          if(this.mWebHostContent.has("spm-pre")) {
            utMap["spm-pre"] = this.mWebHostContent.get("spm-pre");
            //spm-pre这个数据由框架来计算，所以只用报一次，不删除会影响TAB切换的准确性
            this.mWebHostContent.delete("spm-pre");
          }
          if(this.mWebHostContent.has("spm-url")) {
            utMap["spm-url"] = this.mWebHostContent.get("spm-url");
            //spm-url这个数据由框架来计算，所以只用报一次，不删除会影响TAB切换的准确性
            this.mWebHostContent.delete("spm-url");
          }
        }
      }
    }catch (e){
      logger.e("trackPageLeave", e);
    }
  }

}