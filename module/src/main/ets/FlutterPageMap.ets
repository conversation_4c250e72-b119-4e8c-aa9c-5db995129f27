import { Js<PERSON><PERSON> } from "@fliggy-ohos/unicorn";
import { PageContext } from '@fliggy-ohos/router';

export interface TripFlutterPageLifecycleCallbacks {
    onPageShow(page: IFlutterPage): void;
    onPageHide(page: IFlutterPage): void;
    onPageDestroy(page: IFlutterPage): void;
}

export interface IFlutterPage {
    getUniqueId() :string;
    getPageContext(): PageContext;
    getJsBridge(): JsBridge;
    registerFlutterPageLifecycleCallbacks(callbacks: TripFlutterPageLifecycleCallbacks): void;
    unregisterFlutterPageLifecycleCallbacks(callbacks: TripFlutterPageLifecycleCallbacks): void;
}


export const flutterPageMap: Map<string, IFlutterPage> = new Map();