import { FlutterPage } from '@ohos/flutter_ohos';
import { FlutterBoostEntry } from '@fliggy-ohos/flutter_boost';
import { FlutterView } from '@ohos/flutter_ohos/src/main/ets/view/FlutterView';
import { FliggyNavigator, PageContext, RouterUtils, TripTabsController } from '@fliggy-ohos/router';
import { FlutterPageController, logger } from './FlutterPageController';
import { flutterPageMap } from '../FlutterPageMap';
import { WindowUtils } from '@fliggy-ohos/appcompat';
import { TripFlutterBoostEntry } from './TripFlutterBoostEntry';

//用户标记状态栏切换时的页面
let currentUniqueId: string | undefined = '';

@Entry({ routeName: "flutter_view" })
@Component
export struct TripFlutterPage {
  pageContext?: PageContext;
  flutterEntry?: FlutterBoostEntry;
  flutterView?: FlutterView;
  flutterParams?: Record<string, Object>;
  private uniqueId?: string;
  private lastBackPressTime: number = 0;
  flutterPageController?: FlutterPageController;
  flutterIsInitDone?: boolean = false;
  isTransparent: boolean = false;
  isStatusBarDark: boolean = true;

  aboutToAppear() {
    this.pageContext?.bind(this);
    this.flutterIsInitDone = AppStorage.get<boolean>('FlutterIsInitDone')
    logger.d('aboutToAppear', `flutterIsInitDone: ${this.flutterIsInitDone}`);
    if(this.flutterIsInitDone) {

      if (!this.flutterParams) {
        this.flutterParams = {};
        this.flutterParams.uri = this.pageContext!.pageUrl.getPath();
        // 对齐三端
        let paramsRecord :Record<string, Object> = {
          '__flutter_arguments__': JSON.stringify(this.pageContext!.params)
        };
        this.flutterParams.params = paramsRecord;
      }

      this.isTransparent = RouterUtils.isBackgroundTransparent(this.pageContext);
      this.flutterEntry = new TripFlutterBoostEntry(this.pageContext! ,getContext(this), this.flutterParams, this.isTransparent);
      this.flutterEntry.onReady(FliggyNavigator.getDefaultRouter())
      this.flutterEntry.aboutToAppear()
      this.flutterView = this.flutterEntry.getFlutterView()

      this.uniqueId = this.flutterEntry.getUniqueId();
      this.flutterPageController = new FlutterPageController(this);
      this.flutterPageController.setUniqueId(this.uniqueId);
      this.flutterPageController.dispatchLifeCycle("aboutToAppear");
      this.flutterPageController.setWebviewId(this.flutterView?.getId());
      flutterPageMap.set(this.uniqueId, this.flutterPageController);

      //默认设置为黑色
      this.changeStatusBarStyle(true);

      currentUniqueId = this.uniqueId
    }
  }

  aboutToDisappear() {
    this.flutterEntry?.aboutToDisappear()

    this.flutterPageController?.dispatchLifeCycle("aboutToDisappear");
    flutterPageMap.delete(this.uniqueId ?? '');

    currentUniqueId = '';
  }

  onPageShow() {
    this.flutterEntry?.onPageShow();
    this.flutterPageController?.dispatchLifeCycle("onPageShow");

    if (currentUniqueId != this.uniqueId) {
      //回到页面恢复状态栏颜色
      this.changeStatusBarStyle(this.isStatusBarDark);
      currentUniqueId = this.uniqueId
    }
  }

  onPageHide() {
    this.flutterEntry?.onPageHide()
    this.flutterPageController?.dispatchLifeCycle("onPageHide");

    if (currentUniqueId == this.uniqueId) {
      //离开页面设置为黑色，保存一下状态
      this.isStatusBarDark = WindowUtils.getWindowSystemBarProperties()?.statusBarContentColor === Color.Black.toString()
    }
  }

  build() {
    Stack() {
      if(this.flutterIsInitDone) {
        FlutterPage({ viewId: this.flutterView?.getId() })
      }
    }
  }

  onBackPress(): boolean {
    //如果在 1 秒内，用户按了 2 次返回键，则直接返回，防止 flutter 出问题，关不了当前页
    if (Date.now() - this.lastBackPressTime < 1000) {
      return false;
    }
    this.lastBackPressTime = Date.now();
    return this.flutterEntry?.onBackPressed() ?? false
  }

  changeStatusBarStyle(dark: boolean) {
    WindowUtils.setWindowSystemBarProperties(
      {
        statusBarContentColor: dark ? Color.Black.toString() : Color.White.toString(),
      }
    )
  }
}
