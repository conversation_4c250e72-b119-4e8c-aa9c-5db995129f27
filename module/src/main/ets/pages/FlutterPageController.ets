import { Logger } from '@fliggy-ohos/logger';
import {
  BridgeInterceptor,
  ITrackAdapter, IUIAdapter, IWebView, JsBridge, JsCallback, Params } from '@fliggy-ohos/unicorn';
import webview from '@ohos.web.webview';
import { TitleBarController } from '@fliggy-ohos/titlebar';
import { navigator, PageContext } from '@fliggy-ohos/router';
import { TripFlutterPage } from './TripFlutterPage';
import { IFlutterPage, TripFlutterPageLifecycleCallbacks } from '../FlutterPageMap';
import { FbridgePlugin } from '../plugins/fbridge/FbridgePlugin';
import { TrackAdapterImpl } from '../track/TrackAdapterImpl';
import componentSnapshot from '@ohos.arkui.componentSnapshot';
import { AsyncCallback } from '@ohos.base';
import { image } from '@kit.ImageKit';

export const logger = new Logger('flutter');

export class FlutterPageController implements IFlutterPage, IWebView, BridgeInterceptor {

  private page: TripFlutterPage;
  private uniqueId?: string;
  private jsBridge: JsBridge;
  private flutterPageLifecycleCallbacks: TripFlutterPageLifecycleCallbacks[] = [];

  private _webviewId: string = "webview_content";

  private trackAdapter?: ITrackAdapter;

  private isReady = false;
  private isSendResumeEvent = false;

  constructor(page: TripFlutterPage) {
    this.page = page;

    this.jsBridge = new JsBridge();
    this.jsBridge.setup(this);
    this.jsBridge.setHost("flutter");
    this.jsBridge.setInterceptor(this);

    this.trackAdapter = new TrackAdapterImpl(this.page.pageContext!);
  }

  onCallMethod(method: string, params: Params, succeedCallback: JsCallback, failedCallback: JsCallback): boolean {
    if ("ready" === method) {
      this.isReady = true;
      // if (params != null && params.containsKey("poplayer_extra_params")) {
      //   poplayerExtraParams = (Map<String, String>) params.get("poplayer_extra_params");
      // }

      if (!this.isSendResumeEvent) {
        this.fireLifeCycleEvent("WV.Resume");
      }
    }
    return true;
  }

  getPageContext(): PageContext {
    return this.page.pageContext!;
  }

  loadUrl(url: string, headers?: webview.WebHeader[] | undefined): void {

  }

  refresh(): void {

  }

  getUrl(): string {
    return "";
  }

  back(): boolean {
    navigator.popToBack(this.page.pageContext);
    return true;
  }

  call2Js(jsFunctionName: string, responseData: string): void {

  }

  fireEvent(event: string, data: string): void {
    let plugin = this.page.flutterEntry?.getFlutterEngine()?.getPlugins()?.get("FbridgePlugin");
    if (plugin instanceof FbridgePlugin) {
      plugin.getMethodChannel()?.invokeMethod(event, data);
    }
  }

  screenshot(type: string, callback: AsyncCallback<image.PixelMap>): void {
    try {
      let pixelMap = componentSnapshot.getSync(this._webviewId);
      callback(undefined, pixelMap);
    } catch (e) {
      callback(e, undefined);
    }
  }

  getUserAgent(): string {
    return "";
  }

  evaluateJavascript(script: string, resultCallback: (result: string) => void): void {

  }

  isPoplayer(): boolean {
    return false;
  }

  setPoplayer(value: boolean): void {
  }

  isTransparent(): boolean {
    return false;
  }

  setTransparent(transparent: boolean): void {

  }

  getUIAdapter(): IUIAdapter | undefined {
    return undefined;
  }

  getTitleBarController(): TitleBarController | undefined {
    return undefined;
  }

  getTrackAdapter(): ITrackAdapter | undefined {
    return this.trackAdapter;
  }

  destroy(): void {

  }

  setUniqueId(uniqueId: string): void {
    this.uniqueId = uniqueId;
  }

  getUniqueId(): string {
    return this.uniqueId!;
  }

  public setWebviewId(value: string) {
    this._webviewId = value;
  }

  public getWebviewId(): string {
    return this._webviewId;
  }

  getJsBridge(): JsBridge {
    return this.jsBridge;
  }

  public registerFlutterPageLifecycleCallbacks(callbacks: TripFlutterPageLifecycleCallbacks): void {
    if (callbacks) {
      this.flutterPageLifecycleCallbacks.push(callbacks);
    }
  }

  public unregisterFlutterPageLifecycleCallbacks(callbacks: TripFlutterPageLifecycleCallbacks): void {
    if (callbacks) {
      const index = this.flutterPageLifecycleCallbacks.indexOf(callbacks);
      if (index > -1) {
        this.flutterPageLifecycleCallbacks.splice(index, 1);
      }
    }
  }

  public dispatchLifeCycle(state: string): void {
    try {
      logger.d('printLifeCycle', `${state}: ${this.page.flutterParams?.url} ${this.page.flutterEntry?.getUniqueId()}`);
      switch (state) {
        case "onPageShow":
          this.jsBridge.onResume();
          for (let callbacks of this.flutterPageLifecycleCallbacks) {
            callbacks.onPageShow(this);
          }
          this.fireLifeCycleEvent("WV.Resume");
          break;
        case "onPageHide":
          this.jsBridge.onPause();
          this.trackAdapter?.trackPageLeave();
          for (let callbacks of this.flutterPageLifecycleCallbacks) {
            callbacks.onPageHide(this);
          }
          this.fireLifeCycleEvent("WV.Pause");
          break;
        case "aboutToDisappear":
          this.jsBridge.onDestroy();
          for (let callbacks of this.flutterPageLifecycleCallbacks) {
            callbacks.onPageDestroy(this);
          }
          break;
      }
    } catch (e) {
      logger.e('printLifeCycle', e);
    }
  }

  private fireLifeCycleEvent(event: string): void {
    if (this.isReady) {
      let params: Record<string, string> = {
        '__container_uniqueId_key__': this.uniqueId!,
      };
      this.fireEvent(event, JSON.stringify(params));
      if (!this.isSendResumeEvent && event === "WV.Resume") {
        this.isSendResumeEvent = true;
      }
    }
  }
}
