import { FlutterBoostEntry } from '@fliggy-ohos/flutter_boost';
import { navigator } from '@fliggy-ohos/router';
import { PageContext } from '@fliggy-ohos/router';


export class TripFlutterBoostEntry extends FlutterBoostEntry {

  pageContext?: PageContext;

  constructor(pageContext: PageContext, context: Context, routerOptions: ESObject = {}, isDialog: boolean = false) {
    super(context, routerOptions, isDialog);
    this.pageContext = pageContext;
  }


  finishContainer(result: Record<string, Object>) {
    navigator.popToBack(this.pageContext, result);
  }

}