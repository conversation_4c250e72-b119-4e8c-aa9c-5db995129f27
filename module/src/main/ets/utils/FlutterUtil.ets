import { configCenter } from '@fliggy-ohos/configcenter';
import { Logger } from '@fliggy-ohos/logger';
import { FlutterBoost } from '@fliggy-ohos/flutter_boost';
import { FlutterEngine, FlutterPlugin } from '@ohos/flutter_ohos';
import { FbridgePlugin } from '../plugins/fbridge/FbridgePlugin';
import { JSON } from '@kit.ArkTS';
import { OnFlutterResult } from '../jsbridge/OnFlutterResult';

// let logger = new Logger('FlutterUtil');

export interface OnResult {
  onResult: (result: Object) => void;
}

export class FlutterUtil {
  private static readonly TAG = 'FlutterUtil';
  private static readonly CONFIG_GROUP = 'trip-flutter';
  private static readonly logger = new Logger(FlutterUtil.TAG);

  // static getString(key: string, defaultVal: string): string {
  //   const ret = configCenter.getString(FlutterUtil.CONFIG_GROUP, key, defaultVal);
  //   logger.d('flutter.getConfig', `getString: key=${key}, value=${ret}`);
  //   return ret;
  // }
  //
  // static getInt(key: string, defaultVal: number): number {
  //   const ret = configCenter.getNumber(FlutterUtil.CONFIG_GROUP, key, defaultVal);
  //   logger.d('flutter.getConfig', `getInt: key=${key}, value=${ret}`);
  //   return ret;
  // }
  //
  // static getBoolean(key: string, defaultVal: boolean): boolean {
  //   const ret = configCenter.getBoolean(FlutterUtil.CONFIG_GROUP, key, defaultVal);
  //   logger.d('flutter.getConfig', `getBoolean: key=${key}, value=${ret}`);
  //   return ret;
  // }

  // static checkBlackList(key: string, url: string): boolean {
  //   try {
  //     const blackJson = FlutterUtil.getString(key, '[]');
  //     const ary = JSON.parse(blackJson);
  //
  //     for (let i = 0; i < ary.length; i++) {
  //       const regex = ary[i];
  //       if (regex) {
  //         const matcher = new RegExp(regex).test(url);
  //         if (matcher) {
  //           return true;
  //         }
  //       }
  //     }
  //   } catch (e) {
  //     logger.e(FlutterUtil.TAG, url, e as Error);
  //   }
  //
  //   return false;
  // }

  // static isBackgroundTransparent(uri: URLSearchParams): boolean {
  //   try {
  //     const isUnify = FlutterUtil.getBooleanQueryParameter(uri, '_fli_background_transparent', false);
  //     return isUnify;
  //   } catch (e) {
  //     logger.e(FlutterUtil.TAG, e as Error);
  //     return false;
  //   }
  // }
  //
  // static getBooleanQueryParameter(uri: URLSearchParams, key: string, defaultValue: boolean): boolean {
  //   const flag = uri.get(key);
  //   if (flag === null) {
  //     return defaultValue;
  //   } else {
  //     flag = flag.toLowerCase();
  //     return ['true', '1'].includes(flag);
  //   }
  // }

  static fdispatchEvent(bizName: string, methodName: string, args?: Record<string, Object>): void {
    const engine = FlutterBoost.getInstance().getEngine()
    if (engine && engine?.getPlugins()?.has("FbridgePlugin")) {
      const value : Record<string, Object>= {};
      if (args) {
        Object.keys(args).forEach((key) => {
          value[key] = args[key];
        });
      }
      try {
        const channel = (engine?.getPlugins()?.get("FbridgePlugin") as FbridgePlugin)?.getMethodChannel();
        if (bizName.length > 0) {
          channel?.invokeMethod(`${bizName}/${methodName}`, JSON.stringify(value));
        } else {
          channel?.invokeMethod(methodName, JSON.stringify(value));
        }
      } catch (e) {
        FlutterUtil.logger.e("fdispatchEvent",e);
      }
    } else {
      FlutterUtil.logger.w("fdispatchEvent",`fdispatchEventForResult engine not ready, bizName:${bizName} methodName:${methodName}`);
    }
  }

  static fdispatchEventForResult(bizName: string, methodName: string, args?: Record<string, Object>, onResult?: OnResult): void {
    const engine = FlutterBoost.getInstance().getEngine()
    if (engine && engine?.getPlugins()?.has("FbridgePlugin")) {
      const value : Record<string, Object>= {};
      if (args) {
        Object.keys(args).forEach((key) => {
          value[key] = args[key];
        });
      }
      try {
        let invokeName = methodName;
        if (bizName.length > 0) {
          invokeName = `${bizName}/${methodName}`;
        }

        if (onResult) {
          const _callbackTag = Date.now().toString();
          OnFlutterResult.onResultCache.set(invokeName + '_' + _callbackTag, onResult);
          value['_callback_tag'] =  _callbackTag;
        }
        const channel = (engine?.getPlugins()?.get("FbridgePlugin") as FbridgePlugin)?.getMethodChannel();
        channel?.invokeMethod(invokeName, JSON.stringify(value));
      } catch (e) {
        FlutterUtil.logger.e("fdispatchEventForResult",e);
      }
    } else {
      FlutterUtil.logger.w("fdispatchEventForResult",`fdispatchEventForResult engine not ready, bizName:${bizName} methodName:${methodName}`);
    }
  }

  static fbroadcast(name: string, sticky: boolean, args?: Record<string, Object>): void {
    const engine = FlutterBoost.getInstance().getEngine()
    if (engine && engine?.getPlugins()?.has("FbridgePlugin")) {
      const value : Record<string, Object>= {};
      value["name"] = name;
      value["sticky"] = sticky;
      if (args) {
        const temp : Record<string, Object>= {};
        Object.keys(args).forEach((key) => {
          temp[key] = args[key];
        });
        value['args'] = temp;
      }
      try {
        try {
          const channel = (engine?.getPlugins()?.get("FbridgePlugin") as FbridgePlugin)?.getMethodChannel();
          channel?.invokeMethod('fbroadcast', JSON.stringify(value));
        } catch (e) {
          FlutterUtil.logger.e("fbroadcast",e);
        }
      } catch (e) {
        FlutterUtil.logger.e("fbroadcast",e);
      }
    } else {
      FlutterUtil.logger.w("fbroadcast",`engine not ready, name:${name}`);
    }
  }

  // static clearNavTime(args: Record<string, any>): void {
  //   if (args) {
  //     const removeKey = '_fli_nav_time';
  //     if (args[removeKey]) {
  //       delete args[removeKey];
  //     }
  //
  //     try {
  //       if (args['url_param']) {
  //         const params = args['url_param'] as Record<string, any>;
  //         const jsonObject = JSON.parse(params['__flutter_arguments__']);
  //         if (jsonObject[removeKey]) {
  //           delete jsonObject[removeKey];
  //         }
  //
  //         params['__flutter_arguments__'] = JSON.stringify(jsonObject);
  //       }
  //     } catch (e) {
  //       logger.e('', e as Error);
  //     }
  //   }
  // }
}