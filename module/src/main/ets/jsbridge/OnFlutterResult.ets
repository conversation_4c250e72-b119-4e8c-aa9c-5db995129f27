import { JsApiPlugin, JsCallBackContext, Params } from '@fliggy-ohos/unicorn';
import { FlutterUtil, OnResult } from '../utils/FlutterUtil';
import { JSON } from '@kit.ArkTS';

// @JsApiMetaData(["on_flutter_result"], 0，)
export class OnFlutterResult extends JsApiPlugin {
  public static readonly onResultCache = new Map<String, OnResult>();

  protected execute(method: string, params: Params, callback: JsCallBackContext): void {
    let invokeName = params.getString("_event_")
    let _callback_tag = params.getString("_callback_tag")
    let onResultCallback= OnFlutterResult.onResultCache.get(invokeName + "_" + _callback_tag)
    if (onResultCallback) {
      onResultCallback.onResult(params.get("result") ?? Object());
    }
  }


}
