import { JsApiPlugin, JsCallBackContext, Params } from '@fliggy-ohos/unicorn';
import { FlutterUtil } from '../utils/FlutterUtil';
import { JSON } from '@kit.ArkTS';

// @JsApiMetaData(["fbroadcast","fdispatch_event"], 0，)
export class FBroadcastPlugin extends JsApiPlugin {
  protected execute(method: string, params: Params, callback: JsCallBackContext): void {
    if (method === 'fbroadcast') {
      this.executeFbroadcast(params, callback);
    } else if (method === 'fdispatch_event') {
      this.executeFdispatchEvent(params, callback);
    } else {
      callback.errorString(`No such bridge method: ${method}`);
    }
  }

  executeFbroadcast(params: Params, callback: JsCallBackContext): void {
    let broadcastName = params.getString("name")
    if (!broadcastName || broadcastName.length === 0) {
      callback.errorString(`Bridge call error, no required param "name" is provided! Stack:\nBridge.call("fbroadcast", ${JSON.stringify(params)})`);
      return;
    }
    let isSticky = params.getBool("is_sticky") ?? false;
    let broadcastData = params.get("data") as Record<string, Object>
    FlutterUtil.fbroadcast(broadcastName, isSticky, broadcastData);
    callback.success();
  }

  executeFdispatchEvent(params: Params, callback: JsCallBackContext): void {
    let name = params.getString("name") ?? ''
    let biz = params.getString("biz") ?? ''
    if (name.length === 0 || biz.length === 0) {
      callback.errorString(`Bridge call error, no required param "name" is provided! Stack:\nBridge.call("fdispatch_event", ${JSON.stringify(params)})`);
      return;
    }
    let needResult = params.getBool("need_result") ?? false;
    let data = params.get("data") as Record<string, Object>
    if (needResult) {
      FlutterUtil.fdispatchEventForResult(biz, name, data, {
        onResult(result) {
          callback.successString(JSON.stringify(result));
        }
      })
    } else {
      FlutterUtil.fdispatchEvent(biz, name, data);
      callback.success();
    }
  }
}
