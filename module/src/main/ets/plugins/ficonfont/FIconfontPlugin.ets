import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  Method<PERSON>allHand<PERSON>,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { camelCase } from '@fliggy-ohos/commonutils';
import { Logger } from '@fliggy-ohos/logger';
import { FiconAsset } from './FiconAsset';
import { tracker } from "@fliggy-ohos/tracker";
import { HashMap } from '@kit.ArkTS';


const TAG: string = "FIconfontPlugin";
const logger = new Logger(TAG);

export class FIconfontPlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private context: Context | undefined;
  private channel: MethodChannel | undefined;

  constructor(context?: Context) {
    if (context) {
      this.context = context;
    }
  }

  // 获取唯一的类名 类似安卓的Class<? extends FlutterPlugin ts无法实现只能用户自定义
  getUniqueClassName(): string {
    return 'FIconfontPlugin'
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    // 名称对应于flutter fportal中的lib/iconfont_init.dart https://code.alibaba-inc.com/fliggy_mobile/fportal/blob/master/lib/iconfont_init.dart#L10
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "ficonfont");
    this.channel.setMethodCallHandler(this);
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d(TAG, "--------onMethodCall -------" + call.method);
    const method: string = camelCase(call.method)
    /**
     * iconfont 资源文件所在的bundle
     */
    const iconfontModuleName: string = "entry"
    /**
     * iconfont 资源文件路径
     */
    const iconfontPath: string = "fonts/icon_font.ttf";

    try {
      let params: Map<string, object> = new Map<string, object>();
      if(call.args instanceof  Map) {
        params = call.args;
      }
      switch (method) {
        case 'getIconFontStream':
        // 确保 this.context 不为 undefined
          if (this.context) {
            if (getBool(params,'promise') == true || getString(params,'promise') == 'true') {
              FiconAsset.getResourceFileStream(this.context, iconfontModuleName, iconfontPath).then((value) => {
                result.success(value);
              }
              );
            } else {
              // 目前使用这个 会比较快
              result.success(FiconAsset.getResourceFileStreamSync(this.context, iconfontModuleName, iconfontPath));
            }
          } else {
            result.error("${TAG} Context is undefined", null, null);
          }
          break;
        case 'FliggyUserTrackApiCustom':
          result.success(this.custom(call.args));
          break;
        default:
          result.notImplemented();
          break;
      }
    } catch (e) {
      result.error("${TAG} ${e.message}", e.stack, null);
    }
  }



  /**
   * 自定义埋点 原先没有埋点channel的时候加了 FliggyUserTrackApiCustom 的channel调用来上报异常
   * @param params 参数
   */
  private custom(params: object | undefined) {
    if (params) {
      let map: Map<string, object> = new Map<string, object>();
      if(params instanceof  Map) {
        params = params;
      }
      let pageName: string = map['pageName'] ?? 'UT';
      let event: string = map['event'] ?? '';
      let trackMap: Map<string, string> = new Map(Object.entries(map));
      tracker.trackOriginalCommitEvent(pageName, 19999, event, '', '0', trackMap);
    }
  }
}

function getBool(map: Map<string, object>, key: string): boolean | undefined {
  const value = map.get(key);
  if (typeof value === 'boolean') {
    return value;
  } else if (typeof value === 'string') {
    return value === 'true' || value === '1';
  }
  return undefined;
}

function getString(map: Map<string, object>, key: string): string | undefined {
  const value = map.get(key);
  if (typeof value === 'string') {
    return value;
  }
  return undefined;
}

