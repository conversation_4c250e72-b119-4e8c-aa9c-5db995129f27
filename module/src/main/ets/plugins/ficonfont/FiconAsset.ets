import HashMap from '@ohos.util.HashMap';
import { List } from '@kit.ArkTS';
import { Logger } from "@fliggy-ohos/logger";
import util from '@ohos.util';
import common from '@ohos.app.ability.common';
const TAG: string = "FiconAsset";
const logger = new Logger(TAG);

export class FiconAsset {

  constructor() {
  }

  /**
   * 获取资源文件数据流 Obtains the raw file resource corresponding to the specified resource path in callback mode.
   * 参考文档 https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V1/js-apis-resource-manager-0000001630146165-V1#ZH-CN_TOPIC_0000001714626645__getrawfilecontentsync10
   * https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V1/js-apis-inner-application-context-0000001630146093-V1#ZH-CN_TOPIC_0000001666547304__contextcreatemodulecontext
   *
   * @param context
   * @param context
   * @param moduleName
   * @param filePath
   * @returns
   * @param moduleName
   * @param filePath
   * @returns
   * @param context
   * @param moduleName 模块名
   * @param filePath 资源文件路径
   * @returns
   */
  static getResourceFileStream(context: Context, moduleName: string, filePath: string): Promise<Uint8Array | null> {
    return new Promise((resolve, reject) => {
      // 获取要操作的资源管理器实例
      let moduleContext: common.Context;
      try {
        moduleContext = context.createModuleContext(moduleName);
      } catch (error) {
        logger.e(TAG,'createModuleContext failed, error.code: ${error.code}, error.message: ${error.message}');
        return;
      }
      if (!moduleContext) {
        logger.e(TAG, `Unable to create module context for ${moduleName}`);
        return;
      }
      const resourceManager = moduleContext.resourceManager;
      if (!resourceManager) {
        logger.e(TAG, `Resource manager not available for module ${moduleName}`);
        reject(`Resource manager not available for module ${moduleName}`);
        return;
      }

      // 异步获取资源文件内容
      resourceManager.getRawFileContent(filePath, (error, value) => {
        if (error) {
          logger.e(TAG, error.toString());
          reject(error.toString());
        } else {
          // 打印获取到的原始文件内容
          logger.d(TAG, value.toString());
          resolve(value);
        }
      });
    });
  }


  /**
   * 获取资源文件数据流
   * 参考文档 https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V1/js-apis-resource-manager-0000001630146165-V1#ZH-CN_TOPIC_0000001714626645__getrawfilecontentsync10
   * https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V1/js-apis-inner-application-context-0000001630146093-V1#ZH-CN_TOPIC_0000001666547304__contextcreatemodulecontext
   * @param context
   * @param moduleName
   * @param filePath
   * @returns
   * @param context
   * @param moduleName 模块名
   * @param filePath 资源文件路径
   * @returns
   */
  static getResourceFileStreamSync(context: Context, moduleName: string, filePath: string): Uint8Array | null {
    /// context.resourceManager.getRawFileContentSync(filePath) 读取也可以
    // 获取要操作的资源管理器实例
    let moduleContext: common.Context;
    try {
      moduleContext = context.createModuleContext(moduleName);
    } catch (error) {
      logger.e(TAG,'createModuleContext failed, error.code: ${error.code}, error.message: ${error.message}');
      return null;
    }
    if (!moduleContext) {
      logger.e(TAG, `Unable to create module context for ${moduleName}`);
      return null;
    }
    const resourceManager = moduleContext.resourceManager;
    if (!resourceManager) {
      logger.e(TAG, `Resource manager not available for module ${moduleName}`);
      return null;
    }
    try {
      let value: Uint8Array = resourceManager.getRawFileContentSync(filePath);
      return value;
    } catch (error) {
      logger.e(TAG,error.toString())
    }
    return null;
  }

}