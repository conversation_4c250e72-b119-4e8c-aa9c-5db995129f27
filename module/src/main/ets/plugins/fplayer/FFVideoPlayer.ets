import { FPlayerController, VideoUrlType} from '@fliggy-ohos/player';
import media from '@ohos.multimedia.media'
import audio from "@ohos.multimedia.audio";
import prompt from '@ohos.promptAction';
import resourceManager from '@ohos.resourceManager';
import window from '@ohos.window';
import Log from '@ohos/flutter_ohos/src/main/ets/util/Log';
import { SurfaceTextureEntry } from  '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
import image from '@ohos.multimedia.image';
import { EventChannel, MethodCall } from '@ohos/flutter_ohos';
import { MethodChannel } from '@ohos/flutter_ohos';
import { EventSink, StreamHandler } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { PlayerEventSink } from './PlayerEventSink';
import { ArrayList, HashMap, url } from '@kit.ArkTS';
import { IncomingMethod<PERSON><PERSON><PERSON><PERSON><PERSON>,
  MethodCallHandler,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

export class FFVideoPlayer {
  private player: FPlayerController = new FPlayerController();

  private textureEntry: SurfaceTextureEntry;
  private channel: MethodChannel | null = null;
  private eventSink: PlayerEventSink | null = null;
  private surfaceId: string = '';

  constructor(textureEntry: SurfaceTextureEntry, methodChannel: MethodChannel) {
    this.textureEntry = textureEntry;
    this.surfaceId = textureEntry.getSurfaceId().toString();
    this.channel = methodChannel;
    this.channel?.setMethodCallHandler(new PlayerMethodCallHandler(this.player));
  }

  async createPlayer(args: Map<String, Object>) {
    let url = (args.get('url') ?? '') as string;
    let loop = (args.get('loop') ?? false) as boolean;
    let useCache = (args.get('useCache') ?? false) as boolean;// 暂不支持
    let cachePlayer = (args.get('cachePlayer') ?? false) as boolean;// 暂不支持
    let cacheKey = (args.get('cachePlayer') ?? '') as string;// 暂不支持
    let breakAudio = (args.get('breakAudio') ?? false) as boolean;
    let autoPlay = (args.get('autoPlay') ?? true) as boolean;
    let muted = (args.get('muted') ?? true) as boolean;
    let urlTypeStr = (args.get('videoUrlType') ?? 'NetVideoUrl') as String;
    let videoUrlType: VideoUrlType = VideoUrlType.NetVideoUrl;
    if (urlTypeStr == 'LocalVideoUrl') {
      videoUrlType = VideoUrlType.LocalVideoUrl;
    } else if (urlTypeStr == 'PhotoVideoUrl') {
      videoUrlType = VideoUrlType.PhotoVideoUrl
    }

    await this.player.createPlayer(
      null,
      url,
      videoUrlType,
      this.surfaceId,
      loop,
      muted,
      media.VideoScaleType.VIDEO_SCALE_TYPE_FIT_CROP,
      autoPlay,
      breakAudio ? audio.InterruptMode.INDEPENDENT_MODE : audio.InterruptMode.SHARE_MODE,
      (controller) => {
        // 视频prepared
        this.player = controller;
        this.sendMethod('fplayer#prepared', {
          'duration' : this.player?.player?.duration,
          'naturalWidth' : this.player?.player?.width,
          'naturalHeight' : this.player?.player?.height
        });
      },
      () => {
        // 播放
        this.sendMethod('fplayer#play', {});
      },
      () => {
        // start
        this.sendMethod('fplayer#start', {});
      },
      () => {
        // pause
        this.sendMethod('fplayer#pause', {});
      },
      () => {
        // complete
        this.sendMethod('fplayer#complete', {});
      },
      () => {
        //startRenderFrame
        this.sendMethod('fplayer#firstVideoFrameRendered', {});
      },
      (error) => {
        this.sendMethod('fplayer#error', {
          'errorCode' : error.code,
          'errorInfo' : error.message
        })
      },
      (currentTime: Number, allTime: Number) => {
        // onDurationUpdate
        this.sendMethod('fplayer#timeChanges', {
          'time' : currentTime
        })
      },
    );
  }

  private sendMethod(name:string, args: ESObject) {
    if (this.channel != null) {
      this.channel.invokeMethod(name, args);
    }
  }
}

class PlayerMethodCallHandler implements MethodCallHandler {
  private player: FPlayerController = new FPlayerController();
  constructor(player: FPlayerController) {
    this.player = player;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    if (call.method == 'fplayer#pause') {
      this.player.pause();
    } else if (call.method == 'fplayer#play') {
      this.player.play();
    } else if (call.method == 'fplayer#seekTo') {
      let time = call.args.get('time') as number;
      this.player.seekTo(time);
    } else if (call.method == 'fplayer#destroy') {
      this.player.destroy();
    } else if (call.method == 'fplayer#refresh') {
      let url = call.args.get('url') as string;
      this.player.refreshWithUrl(url);
    } else if (call.method == 'fplayer#setMute') {
      this.player.setMute(call.args.get('mute') as boolean);
    } else if (call.method == 'fplayer#resume') {
      this.player.play();
    }
  }
}
