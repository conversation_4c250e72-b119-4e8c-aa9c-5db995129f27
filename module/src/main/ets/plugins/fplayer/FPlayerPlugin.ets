import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  Method<PERSON>allHandler,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

import { camelCase } from '@fliggy-ohos/commonutils';
import HashMap from '@ohos.util.HashMap';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { TextureRegistry } from '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
import { SurfaceTextureEntry } from '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
import { FFVideoPlayer } from './FFVideoPlayer';
import { EventChannel } from '@ohos/flutter_ohos';
import { Logger } from "@fliggy-ohos/logger";

const TAG: string = "FPlayerPlugin";
const logger = new Logger(TAG);

type FPlayerCall = (call: MethodCall, result: MethodResult) => void;

export class FPlayerPlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private context: Context | undefined;
  private channel: MethodChannel | undefined;
  private callMap?: HashMap<string, FPlayerCall>;

  private players: Map<String, FFVideoPlayer> = new Map();
  private flutterState: PlayerFlutterState | null = null;

  constructor(context?: Context) {
    if(context) {
      this.context = context;
    }
    this.callMap = new HashMap<string, FPlayerCall>();
    this.callMap.set('create', this.create);
  }

  getUniqueClassName(): string {
    return "FPlayerPlugin";
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "fplayer");
    this.channel.setMethodCallHandler(this);
    this.flutterState = new PlayerFlutterState(this.pluginBinding.getBinaryMessenger(), this.pluginBinding.getTextureRegistry());
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d("fplayer", "--------onMethodCall -------" + call.method);
    const method: string = camelCase(call.method)

    if (call.method == 'create') {
      this.create(call,result);
    }
  }

  async  create(call: MethodCall, result: MethodResult) {
    let flutterRenderer = this.flutterState?.getTextureRegistry();
    let textureId: number = flutterRenderer!.getTextureId();
    let surfaceTextureEntry: SurfaceTextureEntry  = flutterRenderer!.registerTexture(textureId);
    let methodChannel: MethodChannel = new MethodChannel(this.flutterState!.getBinaryMessenger(), "fplayer_" + textureId.toString())
    let player: FFVideoPlayer = new FFVideoPlayer(surfaceTextureEntry, methodChannel);
    await player.createPlayer(call.args);
    this.players["fplayer_" + textureId.toString()] = player;

    result.success({
      'textureId' : textureId
    });
  }
}

export class PlayerFlutterState {
  private binaryMessenger: BinaryMessenger;
  private textureRegistry: TextureRegistry;

  constructor(binaryMessenger: BinaryMessenger, textureRegistry: TextureRegistry) {
    this.binaryMessenger = binaryMessenger;
    this.textureRegistry = textureRegistry;
  }

  getBinaryMessenger(): BinaryMessenger {
    return this.binaryMessenger;
  }

  getTextureRegistry(): TextureRegistry {
    return this.textureRegistry;
  }
}