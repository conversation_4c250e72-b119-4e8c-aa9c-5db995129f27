# Flutter WebView 自动刷新问题修复

## 问题描述

在Flutter WebView中，当用户从WebView页面跳转到其他页面，然后返回时，WebView会自动刷新，导致用户丢失当前浏览状态。

## 问题根因分析

1. **onReady回调重复触发**：WebView组件每次准备就绪时都会调用onReady回调，导致重复调用`loadUrl()`
2. **页面生命周期管理不当**：页面返回时WebView组件重新初始化，触发URL重新加载
3. **缺少状态管理**：没有机制来跟踪URL是否已经加载过

## 解决方案

### 1. 添加URL加载状态标记

在`FlutterWebview`类中添加`isUrlLoaded`标记位：

```typescript
private isUrlLoaded: boolean = false; // 防止重复加载URL
```

### 2. 修改onReady回调逻辑

在`TripWebViewBuilder`和`buildOhosWebView`中添加URL加载状态检查：

```typescript
onReady: () => {
  // 检查是否已经加载过URL，避免重复加载
  if (params["flutterWebviewInstance"] && !params["flutterWebviewInstance"].isUrlLoaded) {
    params["controller"].loadUrl(params["url"]);
    params["flutterWebviewInstance"].isUrlLoaded = true;
  }
}
```

### 3. 改进页面生命周期管理

在`onPageShow`和`onPageHide`方法中添加日志和状态保持逻辑：

```typescript
onPageShow(page: IFlutterPage) {
  logger.d("onPageShow", `viewId=${this.viewId}, isUrlLoaded=${this.isUrlLoaded}`);
  this.controller!.onPageShow();
  // 页面显示时不重新加载URL，保持当前状态
}

onPageHide(page: IFlutterPage) {
  logger.d("onPageHide", `viewId=${this.viewId}, isUrlLoaded=${this.isUrlLoaded}`);
  this.controller!.onPageHide();
  // 页面隐藏时保持URL加载状态，避免返回时重新加载
}
```

### 4. 添加手动重置功能

提供`resetUrlLoadState`方法，允许在需要时手动重置URL加载状态：

```typescript
resetUrlLoadState(call: MethodCall, result: MethodResult) {
  logger.d("resetUrlLoadState", `viewId=${this.viewId}, resetting URL load state`);
  this.isUrlLoaded = false;
  this.safeCallbackSuccess(result, null);
}
```

## 修改的文件

1. `FlutterWebview.ets` - 主要修改文件
2. `FlutterWebviewPlugin.ets` - 添加新的方法调用支持

## 使用方法

### 正常使用

修改后的WebView会自动防止页面返回时的重复加载，无需额外配置。

### 手动重新加载

如果需要强制重新加载WebView，可以调用：

```dart
// 在Flutter端调用
await webviewController.resetUrlLoadState();
// 然后可以重新加载URL
await webviewController.reloadUrl(newUrl);
```

## 测试验证

### 测试场景

1. **页面跳转返回测试**：
   - 在WebView中浏览内容
   - 跳转到其他页面
   - 返回WebView页面
   - 验证内容是否保持不变

2. **页面生命周期测试**：
   - 观察onPageShow/onPageHide日志
   - 验证URL加载状态是否正确维护

### 调试方法

1. 查看日志输出，关注`isUrlLoaded`状态变化
2. 观察WebView的加载行为
3. 测试不同的页面跳转场景

## 注意事项

1. 修改后WebView默认不会在页面返回时重新加载
2. 如需重新加载，请使用`resetUrlLoadState`方法
3. 缓存的WebView实例会保持其加载状态
4. 建议在开发阶段开启详细日志以便调试

## 兼容性

- 兼容现有的WebView使用方式
- 不影响其他WebView功能
- 向后兼容，无需修改现有调用代码
