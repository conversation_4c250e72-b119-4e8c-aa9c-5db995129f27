import { BinaryMessenger, PlatformView, PlatformViewFactory, StandardMessageCodec } from '@ohos/flutter_ohos';
import { HashMap } from '@kit.ArkTS';
import { FlutterWebview } from './FlutterWebview';
import { flutterPageMap, IFlutterPage, TripFlutterPageLifecycleCallbacks } from '../../FlutterPageMap';

export class FlutterWebviewFactory extends PlatformViewFactory {
  static webviewMap: HashMap<string, FlutterWebview> = new HashMap<string, FlutterWebview>();
  private messenger: BinaryMessenger;

  constructor(messenger: BinaryMessenger) {
    super(new StandardMessageCodec());
    this.messenger = messenger;
  }

  static getWebviewInstance(uniqueId: string): FlutterWebview {
    return FlutterWebviewFactory.webviewMap.get(uniqueId)
  }

  create(context: Context, viewId: number, args: ESObject): PlatformView {
    if (args instanceof Map) {
      const params: Map<string, ESObject> = args as Map<string, ESObject>;

      const uniqueId = params.get("__container_uniqueId_key__") as string;
      const flutterPage = flutterPageMap.get(uniqueId)!;

      const flutterWebview = new FlutterWebview(this.messenger, viewId, uniqueId, flutterPage.getPageContext(), params);
      const webviewId = uniqueId + "_" + viewId;
      FlutterWebviewFactory.webviewMap.set(webviewId, flutterWebview);
      flutterPage.registerFlutterPageLifecycleCallbacks(flutterWebview);
      return flutterWebview;
    }
    throw new Error
  }
}
