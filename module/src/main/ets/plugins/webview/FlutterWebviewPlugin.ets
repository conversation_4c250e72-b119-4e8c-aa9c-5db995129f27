import { H5Utils } from '@fliggy-ohos/unicorn';
import { FlutterPlugin, MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { <PERSON><PERSON><PERSON>Hand<PERSON>, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterWebview } from './FlutterWebview';
import { FlutterWebviewFactory } from './FlutterWebviewFactory';

const TAG:string = 'fliggy_webview_plugin'
export class FlutterWebviewPlugin implements FlutterPlugin, MethodCallHandler {
  channel: MethodChannel | undefined;

  getUniqueClassName(): string {
    return TAG;
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.channel = new MethodChannel(binding.getBinaryMessenger(), TAG)
    this.channel.setMethodCallHandler(this)
    binding.getPlatformViewRegistry().registerViewFactory('fliggy_flutter_webview', new FlutterWebviewFactory(binding.getBinaryMessenger()))
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    switch (call.method) {
      case "close":
        result.success(null);
        break;
      case "eval":
        this.evalJavascript(call, result);
        break;
      case "getUrl":
        this.getUrl(call, result);
        break;
      case "reload":
        this.reload(call, result);
        break;
      case "back":
        this.back(call, result);
        break;
      case "canGoBack":
        this.canGoBack(call, result);
        break;
      case "stopLoading":
        this.stopLoading(call, result);
        break;
      case "reloadUrl":
        this.reloadUrl(call, result);
        break;
      default:
        result.notImplemented();
        break;
    }
  }

  evalJavascript(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      const code: string = call.argument("code");
      flutterWebview!.controller!.evaluateJavascript(code, (value: string) => {
        result.success(value)
      })
    } else {
      result.error("-1", "flutterWebview == null", null);
    }
  }

  getUrl(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      result.success(flutterWebview!.controller!.getUrl())
    } else {
      result.error("-1", "flutterWebview == null", null);
    }

  }

  reload(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      flutterWebview!.controller!.refresh()
      result.success(null)
    } else {
      result.error("-1", "flutterWebview == null", null);
    }
  }

  back(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      flutterWebview!.controller!.back();
      result.success(null)
    } else {
      result.error("-1", "flutterWebview == null", null);
    }
  }

  canGoBack(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      result.success(flutterWebview!.controller!.accessBackward());
    } else {
      result.error("-1", "flutterWebview == null", null);
    }
  }

  stopLoading(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      flutterWebview!.controller!.stop();
      result.success(null);
    } else {
      result.error("-1", "flutterWebview == null", null);
    }
  }

  reloadUrl(call: MethodCall, result: MethodResult) {
    const uniqueId: string = call.argument("__container_uniqueId_key__");
    const viewId: number = call.argument("viewId");
    let flutterWebview = getWebview(uniqueId, viewId);
    if (flutterWebview) {
      let url: string = call.argument("url");
      let headers:object = call.argument("headers");
      if (headers) {
        flutterWebview!.controller!.loadUrl(url, H5Utils.convertToWebHeader(headers));
      } else {
        flutterWebview!.controller!.loadUrl(url);
      }
      result.success(null);
    } else {
      result.error("-1", "flutterWebview == null", null);
    }
  }
}

function getWebview(uniqueId: string, viewId: number): FlutterWebview {
  const webviewId = uniqueId + "_" + viewId;
  return FlutterWebviewFactory.getWebviewInstance(webviewId)
}