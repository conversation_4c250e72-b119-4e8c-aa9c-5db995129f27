import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Method<PERSON>all<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MethodResult,
  PlatformView
} from '@ohos/flutter_ohos';

import { BuilderParams, DVModel } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
import { DVModelJson, DynamicUtils } from './DynamicUtils';
import { createDVModelFromJson } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicViewJson';

import { TripWebViewComponent, TripWebviewController, BridgeInterceptor, JsCallback, Params, TripWebviewControllerCacheMap } from '@fliggy-ohos/unicorn';
import { PageContext } from '@fliggy-ohos/router';
import { IFlutterPage, TripFlutterPageLifecycleCallbacks } from '../../FlutterPageMap';
import { FlutterWebviewFactory } from './FlutterWebviewFactory';
import { Lo<PERSON> } from '@fliggy-ohos/logger';
import { CustomNodePoolFactory, NodeContainerProxy, NodeItem, TypeReuseConfig } from '@hadss/nodepool';
import { TrackConvertUtils } from '@fliggy-ohos/tracker/src/main/ets/utils/TrackConvertUtils';


export const logger = new Logger('FlutterWebview');

const nodePool = new CustomNodePoolFactory().getNodePool({
  typeCount: 5,
  nodeSize: 5,
  expirationTime: 30 * 60 * 1000
});

@Builder
function TripWebViewBuilder(params: object) {
  TripWebViewComponent({
    pageContext: params["pageContext"],
    controller: params["controller"],
    customInjectScript: params["customInjectScript"] ?? [],
    onReady: () => {
      params["controller"].loadUrl(params["url"]);
    },
    onPrompt: params["onPrompt"],
    backgroundTransparent: params["backgroundTransparent"] ?? false,
    widgetUniqueId: params["widgetUniqueId"]
  })
}

let webviewBuilder: WrappedBuilder<ESObject> = wrapBuilder<ESObject>(TripWebViewBuilder);


@Builder
export function buildCacheOhosWebView(arg: ESObject) {
  NodeContainerProxy({ nodeItem: nodePool.getNode(DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "widgetUniqueId"), {
    pageContext: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "pageContext"),
    controller: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "controller"),
    customInjectScript: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "customInjectScript") ?? [],
    url: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "url"),
    onPrompt: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "onPrompt"),
    backgroundTransparent: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "backgroundTransparent") ?? false,
    widgetUniqueId: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "widgetUniqueId")
  }, webviewBuilder) })
}

@Builder
export function buildOhosWebView(arg: ESObject) {
  TripWebViewComponent({
    pageContext: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "pageContext"),
    controller: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "controller"),
    customInjectScript: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "customInjectScript") ?? [],
    onReady: () => {
      DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "controller").loadUrl(DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "url"));
    },
    onPrompt: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "onPrompt"),
    backgroundTransparent: DynamicUtils.getParams(arg?.['platformView']?.['ohosWebviewModel']?.['params'], "backgroundTransparent") ?? false,
  })
}

export class FlutterWebview extends PlatformView implements MethodCallHandler, TripFlutterPageLifecycleCallbacks, BridgeInterceptor {
  controller?: TripWebviewController;
  ohosWebviewModel: DVModel;
  channel: MethodChannel;
  uniqueId: string;
  widgetUniqueId: string;
  viewId: number;
  url: string;
  injectJS: string;
  registerBridges: Array<string> = [];
  backgroundTransparent: boolean;

  constructor(messenger: BinaryMessenger, viewId: number, uniqueId: string, pageContext: PageContext, params: Map<string, ESObject>) {
    super()
    this.uniqueId = uniqueId;
    this.viewId = viewId;

    this.url = params.get("url") as string;
    this.widgetUniqueId = params.get("__widget_uniqueId_key__") as string;
    this.injectJS = params.get("__injectJS__") as string;
    const bridgesObject: ESObject = params.get("__registerBridges__");
    if (Array.isArray(bridgesObject)) {
      this.registerBridges.push(...bridgesObject as string[]);
    }
    this.backgroundTransparent = params.get("__backgroundTransparent__") as boolean;

    if (this.widgetUniqueId) {
      nodePool.setTypeReuseConfig({
        type: this.widgetUniqueId,
        expirationTime: 30 * 60 * 1000, // 老化时间
        reuseCallback: (item: NodeItem) => {
          // 组件复用生命周期回调
          logger.d('reuseCallback', `id:${item.id}, url: ${item.data['url']}`);
        },
        recycleCallback: (item: NodeItem) => {
          // 组件回收生命周期回调
          logger.d('recycleCallback', `id:${item.id}, url: ${item.data['url']}`);
        },
      });

      if (TripWebviewControllerCacheMap.has(this.widgetUniqueId)) {
        this.controller = TripWebviewControllerCacheMap.get(this.widgetUniqueId)!;
        this.controller.getJsBridge().setInterceptor(this);
      }
    }

    if (!this.controller) {
      this.controller = new TripWebviewController(pageContext);
      this.controller.getJsBridge().setInterceptor(this);
    }

    let ohosWebview = new DVModelJson(
      "other",
      [],
      {
        widgetUniqueId: this.widgetUniqueId,
        pageContext: pageContext,
        controller: this.controller!,
        url: this.url,
        onPrompt: this.onPrompt.bind(this),
        customInjectScript: [this.injectJS],
        backgroundTransparent: this.backgroundTransparent
      },
      {},
      this.widgetUniqueId ? buildCacheOhosWebView : buildOhosWebView
    )
    this.ohosWebviewModel = createDVModelFromJson(ohosWebview);

    this.channel = new MethodChannel(messenger, `fliggy_webview_channel_${viewId}_${uniqueId}`);
    this.channel.setMethodCallHandler(this);
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d("onMethodCall", `viewId=${this.viewId}, method=${call.method}, arguments=${JSON.stringify(convertMapToObject(call.args))}`);
    switch (call.method) {
      case "dispatchEventToWeb":
        this.dispatchEventToWeb(call, result);;
        break;
      case "callBridge":
        this.callBridge(call, result);
        break;
      case "registerBridge":
        this.registerBridge(call, result);
        break;
      default:
        result.notImplemented();
    }
  }

  dispatchEventToWeb(call: MethodCall, result: MethodResult): void {
    if (call.args instanceof Map) {
      try {
        let event = call.args.get("event_name") as string;
        let params = this.convertMapToObject(call.args.get("params")) as object;
        this.controller!.fireEvent(event, JSON.stringify(params));
        this.safeCallbackSuccess(result, null);
      } catch (e) {
        this.safeCallbackError(result, "-2", e.message, new Object());
      }
    } else {
      this.safeCallbackError(result, "-1", "arguments is not a map", new Object());
    }
  }

  callBridge(call: MethodCall, methodResult: MethodResult) {
    if (call.args instanceof Map) {
      let method = call.args.get("bridge_name") as string;
      let paramsMap:object = this.convertMapToObject(call.args.get("params")) || new Object();
      let params = new Params(paramsMap);
      this.controller!.getJsBridge().call(method, params, (result) => {
        this.safeCallbackSuccess(methodResult, result);
      }, (result) => {
        try {
          let jsonObject = JSON.parse(result) as object;
          if (jsonObject && jsonObject["errorCode"]) {
            let errorCode = jsonObject["errorCode"] as string;
            let errorMsg = jsonObject["errorMsg"] as string;
            this.safeCallbackError(methodResult, errorCode, errorMsg, jsonObject);
            return;
          }
        } catch (error) {
          this.safeCallbackError(methodResult, "-2", result, new Object());
        }
      });
    } else {
      this.safeCallbackError(methodResult, "-1", "arguments is not a map", new Object());
    }
  }

  registerBridge(call: MethodCall, result: MethodResult) {
    if (call.args instanceof Map) {
      let method = call.args.get("bridge_name") as string;
      if (method) {
        if (this.registerBridges.indexOf(method) < 0) {
          this.registerBridges.push(method);
        }
      }
    } else {
      this.safeCallbackError(result, "-1", "arguments is not a map", new Object());
    }
  }

  onPrompt(method: string, params: string, jsResult: JsResult): boolean {
    this.channel.invokeMethod(method, params, new MethodChannelCallback((result: string) => {
      jsResult.handlePromptConfirm(result);
    }, (result: string) => {
      jsResult.handlePromptConfirm(result);
    }));
    return true;
  }

  /**
   * 桥拦截接口
   * @param method
   * @param params
   * @return 返回 true 继续往后执行，返回 false 中断执行
   */
  onCallMethod(method: string, params: Params, succeedCallback: JsCallback, failedCallback: JsCallback): boolean {
    if (this.registerBridges.includes(method)) {
      this.channel.invokeMethod(method, params, new MethodChannelCallback(succeedCallback, failedCallback));
      return false;
    }
    return true;
  }

  getView(): DVModel {
    return this.ohosWebviewModel
  }

  dispose(): void {

  }

  onPageShow(page: IFlutterPage) {
    this.controller!.onPageShow()
  }

  onPageHide(page: IFlutterPage) {
    this.controller!.onPageHide();
  }

  onPageDestroy(page: IFlutterPage): void {
    this.controller!.destroy();
    const webviewId = this.uniqueId + "_" + this.viewId;
    FlutterWebviewFactory.webviewMap.remove(webviewId);
    nodePool.clearPool();
  }

  private safeCallbackSuccess(methodResult: MethodResult, result: ESObject): void {
    try {
      logger.d("safeCallbackSuccess", `viewId=${this.viewId}, result=${result}`);
      methodResult.success(result);
    } catch (e) {
      logger.e("safeCallbackSuccess", e);
    }
  }

  private safeCallbackError(methodResult: MethodResult, errorCode: string, errorMsg: string, errorObject: object): void {
    try {
      logger.d("safeCallbackError", `viewId=${this.viewId}, errorCode=${errorCode}, errorMsg=${errorMsg}`);
      methodResult.error(errorCode, errorMsg, errorObject);
    } catch (e) {
      logger.e("safeCallbackSuccess", e);
    }
  }

  private convertMapToObject(obj: ESObject | undefined | null): object {
    if (obj instanceof Map) {
      return TrackConvertUtils.convertMapToObject(obj);
    }
    return obj;
  }
}

class MethodChannelCallback implements MethodResult {
  succeedCallback: JsCallback;
  failedCallback: JsCallback;

  constructor(succeedCallback: JsCallback, failedCallback: JsCallback) {
    this.succeedCallback = succeedCallback;
    this.failedCallback = failedCallback;
  }

  success(result: object): void {
    this.succeedCallback(result ? result.toString() : "{}");
  }

  error(errorCode: string, errorMessage: string, errorDetails: object): void {
    let result:object = new Object();
    result["errorCode"] = errorCode;
    result["errorMsg"] = errorMessage;
    this.failedCallback(JSON.stringify(result));
  }

  notImplemented(): void {

  }
}

function convertMapToObject(map: Map<string, Object> | undefined | null): object {
  let obj: object = new Object();
  if (map) {
    for (let key of map.keys()) {
      obj[key] = map.get(key);
    }
  }
  return obj;
}