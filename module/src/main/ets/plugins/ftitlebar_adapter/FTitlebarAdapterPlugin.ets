import { Logger } from "@fliggy-ohos/logger";
import { FlutterPlugin, FlutterPluginBinding, MethodCall, MethodCallHandler,
  MethodChannel,
  MethodResult } from "@ohos/flutter_ohos";


const TAG:string = 'ftitlebar_adapter_plugin'

const logger = new Logger(TAG);

export class FTitlebarAdapterPlugin implements FlutterPlugin, MethodCallHandler {

  channel: MethodChannel | undefined;

  getUniqueClassName(): string {
    return TAG;
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.channel = new MethodChannel(binding.getBinaryMessenger(), TAG)
    this.channel.setMethodCallHandler(this)
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.channel?.setMethodCallHandler(null);
    this.channel = undefined;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d("onMethodCall", "method: " + call.method);
    if (call.method === "getTheme") {
      // IFliggyTheme theme = ThemeManager.getInstance().getFliggyTheme(RunningPageStack.getTopActivity());
      // if (theme != null) {
      //   JSONObject jsonObject = theme.getNavbarData();
      //   if (jsonObject != null) {
      //     result.success(jsonObject.toJSONString());
      //     return;
      //   }
      // }

      result.error("-1", "theme == null", "");
    } else if (call.method === "hasBadge") {
      let badge = undefined;//BadgeManager.getInstance();
      if (badge) {
        //先返回0
        result.success(0);
        // badge.registerListener("Titlebar_*", new BadgeListener() {
        //   public void badgeChanged(String nodeid, NodeItem nodeItem) {
        //     result.success(nodeItem.getCount());
        //     badge.unRegisterListener("Titlebar_*", this);
        //   }
        // });
        // badge.queryNode(new String[]{"Titlebar_*"});
      } else {
        result.error("-1", "badge == null", "");
      }
    } else {
      result.notImplemented();
    }
  }

}