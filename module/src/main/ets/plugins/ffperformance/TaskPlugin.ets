import MethodChannel, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

import { IExecutePlugin, PContext } from '@fliggy-ohos/fperformancekit';

export class TaskPlugin implements  IExecutePlugin {

  private channel: MethodChannel;

  constructor(channel: MethodChannel) {
    this.channel = channel;
  }

  private invokeMethod(method:string, param: Map<string, Object>, result?: MethodResult) {
    let args: Map<string, Object> = new Map<string, Object>();
    args.set('method', method);
    args.set("params", param);
    this.channel.invokeMethod("taskEvent", args, result);
  }

  execute(pContext: PContext): void {
    this.invokeMethod('sendRequest', pContext.toMap(), undefined);
  }
}