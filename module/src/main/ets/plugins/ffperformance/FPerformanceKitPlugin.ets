import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { SingleRequest, FPerformanceKit } from '@fliggy-ohos/fperformancekit'
import { camelCase } from '@fliggy-ohos/commonutils';

import { TaskPlugin } from './TaskPlugin'

const TAG: string = "ffperformance";


export class FPerformanceKitPlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private context: Context | undefined;
  private channel: MethodChannel | undefined;

  constructor(context?: Context) {
    if(context) {
      this.context = context;
    }
  }

  getUniqueClassName(): string {
    return "FPerformanceKitPlugin"
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "ffperformance");
    this.channel.setMethodCallHandler(this);
    FPerformanceKit.getInstance().registerExecutePlugin('flutter', new TaskPlugin(this.channel));
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    const method: string = camelCase(call.method)
    if (method == 'sendRequest') {
      this.sendRequest(call, result);
    }
  }


  private sendRequest(call: MethodCall, result: MethodResult)  {
    if (call.args) {
      let request:SingleRequest = new SingleRequest();
      request.updateFromMap(call.args);
      request.request();
      result.success(undefined);
    }
  }

}