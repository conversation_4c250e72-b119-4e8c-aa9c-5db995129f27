import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  Method<PERSON>allHandler,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

import { globalJsBridge, JsBridge, Params } from '@fliggy-ohos/unicorn';
import { Logger } from '@fliggy-ohos/logger';
import { flutterPageMap } from '../../FlutterPageMap';
import { configCenter } from '@fliggy-ohos/configcenter';
import { env } from '@fliggy-ohos/env';


const TAG: string = "fbridge";
const logger = new Logger(TAG);
type FBridgeCall = (call: MethodCall, result: MethodResult) => void;

/**
 * 记录ios的专属桥，调用时直接返回notImplemented，减少错误埋点上报
 */
const nonReportingBridgeList:Array<string> = ["get_idfa", "swipe_back", "get_caid", "update_swipe_back_params"];

export class FbridgePlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private channel: MethodChannel | undefined;
  private callMap: Map<string, FBridgeCall> = new Map<string, FBridgeCall>([
    ['getConfigs', getConfigs],
    ['getConfig', getConfig],
    ['getEnvironment', getEnvironment],
    ['report_exception', reportException],
    ['isPlatformDebuggable', isPlatformDebuggable],
    ['get_utdid', getUtdid]
  ])

  getUniqueClassName(): string {
    return "FbridgePlugin"
  }

  getMethodChannel(): MethodChannel | undefined {
    return this.channel;
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "fbridge");
    this.channel.setMethodCallHandler(this);
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d("onMethodCall", "method: " + call.method);

    if (nonReportingBridgeList.includes(call.method)) {
      result.notImplemented();
      return;
    }

    const methodName = call.method
    let func: FBridgeCall | undefined = this.callMap.get(methodName)
    if(func) {
      func(call, result)
    } else {
      this.callBridge(call, result)
    }
  }

  getJsBridgeManager(params: Params): JsBridge {
    if (params.hasKey("__container_uniqueId_key__")) {
      let uniqueId = params.getString("__container_uniqueId_key__")!;
      let flutterPage = flutterPageMap.get(uniqueId);
      if (flutterPage) {
        return flutterPage.getJsBridge();
      }
    }

    //找不到时从map中找一个
    for (let uniqueId of flutterPageMap.keys()) {
      logger.e(TAG, `getJsBridgeManager error: uniqueId=${uniqueId}`);
      let flutterPage = flutterPageMap.get(uniqueId);
      if (flutterPage) {
        return flutterPage.getJsBridge();
      }
    }

    return globalJsBridge;
  }

  callBridge(call: MethodCall, result: MethodResult) {
    const bridgeName: string = call.method;
    let params: Params;
    if (call.args) {
      const argStr: string = call.args.toString();
      params = Params.parseJson(argStr);
    } else {
      params = Params.parseJson('{}');
    }
    let bridge: JsBridge = this.getJsBridgeManager(params);
    bridge.call(bridgeName, params, (succ: string) => {
      logger.d(TAG, `fbridge.onSuccess${bridgeName}, ${succ}`)
      result.success(succ);
    }, (err: string) => {
      logger.d(TAG, `fbridge.onFailed${bridgeName}, ${err}`)
      let errObj: object | undefined
      try {
         errObj = JSON.parse(err)
      } catch (ignored) {}
      let errCode = '-1';
      let errMsg = '';
      if(errObj && errObj['errorCode']) {
        errCode = errObj['errorCode']
      }
      if(errObj && errObj['errorMsg']) {
        errMsg = errObj['errorMsg']
      }
      result.error(errCode, errMsg, err)
    });
  }
}

function isPlatformDebuggable(call: MethodCall, result: MethodResult) {
  let debuggable = env.isDebuggable()
  result.success(`${debuggable}`)
}

function reportException(call: MethodCall, result: MethodResult) {
  // nothing to do, depends on AppMonitor
}

function getEnvironment(call: MethodCall, result: MethodResult): void {
  let envStr = env.getEnvironmentString()
  result.success(envStr)
}

function getConfigs(call: MethodCall, result: MethodResult): void {
  if (!call.args) {
    result.error("-2", "no required args when call getConfigs", null)
  } else {
    let args: string = call.args.toString()
    let params = Params.parseJson(args);
    let groupName = params.getString("groupName")
    if (!groupName) {
      result.error("-2", "no required groupName when call getConfigs", null)
    } else {
      let val = configCenter.getConfigs(groupName)
      if (val) {
        let r: Record<string, string> = {}
        r['data'] = JSON.stringify(val)
        r['success'] = 'true'
        result.success(JSON.stringify(r))
      } else {
        result.error("-2", "result is null", null)
      }
    }
  }
}

function getConfig(call: MethodCall, result: MethodResult): void {
  if (!call.args) {
    result.error("-2", "no required args when call getConfig", null)
  } else {
    let args: string = call.args.toString()
    let params = Params.parseJson(args);
    let groupName = params.getString("groupName")
    let key = params.getString("key")
    let defVal = params.getString("defaultValue")
    if (!groupName) {
      result.error("-2", "no required groupName when call getConfig", null)
    } else if (!key) {
      result.error("-2", "no required key when call getConfig", null)
    } else {
      let val = configCenter.getString(groupName, key, defVal ?? '')
      if (val) {
        let r: Record<string, string> = {}
        r['data'] = val
        r['success'] = 'true'
        result.success(JSON.stringify(r))
      } else {
        result.error("-2", "result is null", null)
      }
    }
  }
}

function getUtdid(call: MethodCall, result: MethodResult):void {
  let utdid = env.getUtdid()
  result.success(utdid)
}
