import { isEmpty } from "@fliggy-ohos/commonutils";
import { FliggyKV } from "@fliggy-ohos/fliggykv";
import { Logger } from "@fliggy-ohos/logger";
import { FlutterPlugin, FlutterPluginBinding, <PERSON><PERSON><PERSON>, <PERSON><PERSON>allH<PERSON><PERSON>,
  <PERSON>Channel,
  MethodResult } from "@ohos/flutter_ohos";


const TAG:string = 'fliggy_storage'

const logger = new Logger(TAG);

export class FliggyStoragePlugin implements FlutterPlugin, MethodCallHandler {

  channel: MethodChannel | undefined;

  getUniqueClassName(): string {
    return TAG;
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.channel = new MethodChannel(binding.getBinaryMessenger(), TAG)
    this.channel.setMethodCallHandler(this)
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.channel?.setMethodCallHandler(null);
    this.channel = undefined;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d("onMethodCall", `method: ${call.method}, arguments: ${JSON.stringify(call.args)}`);
    if (call.method === "db_del") {
      this.dbDelete(call, result);
    } else if (call.method === "db_add") {
      this.dbAdd(call, result);
    } else if (call.method === "db_get") {
      this.dbGet(call, result);
    } else {
      result.notImplemented();
    }
  }

  private dbGet(call: MethodCall, callback: MethodResult): void {
    try {
      const key: string = call.argument("key");
      if (isEmpty(key)) {
        callback.error("-1", "k is empty", null);
        return;
      }

      const resString: string | undefined = FliggyKV.default().decodeString(key);

      callback.success({
        success: "true",
        data: resString
      });
    } catch (e) {
      logger.e("DBGet", e);
      callback.error("-1", "invoke error", null);
    }
  }

  private dbAdd(call: MethodCall, callback: MethodResult): void {
    try {
      const key: string = call.argument("key");
      if (isEmpty(key)) {
        callback.error("-1", "k is empty", null);
        return;
      }

      const value: string = call.argument("value");
      if (isEmpty(value)) {
        callback.error("-1", "value is empty", null);
        return;
      }

      FliggyKV.default().encodeString(key, value);

      callback.success({
        success: "true"
      });
    } catch (e) {
      logger.e("DBAdd", e);
      callback.error("-1", "invoke error", null);
    }
  }

  private dbDelete(call: MethodCall, callback: MethodResult): void {
    try {
      const key: string = call.argument("key");
      if (isEmpty(key)) {
        callback.error("-1", "k is empty", null);
        return;
      }

      FliggyKV.default().removeValueForKey(key);

      callback.success({
        success: "true"
      });
    } catch (e) {
      logger.e("DBDel", e);
      callback.error("-1", "invoke error", null);
    }
  }
}