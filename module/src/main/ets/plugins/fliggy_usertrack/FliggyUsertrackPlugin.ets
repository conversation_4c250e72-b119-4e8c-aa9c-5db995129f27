import { AppMonitor, DimensionValueSet, MeasureValueSet, UTAnalytics } from '@taobao-ohos/ut_analytics_sdk';
import { Logger } from "@fliggy-ohos/logger";
import { FlutterPlugin, FlutterPluginBinding, MethodCall, MethodCallHandler,
  MethodChannel,
  MethodResult } from "@ohos/flutter_ohos";
import { JSON } from '@kit.ArkTS';


const TAG:string = 'fliggy_usertrack'

const logger = new Logger(TAG);

export class FliggyUsertrackPlugin implements FlutterPlugin, MethodCallHandler {

  channel: MethodChannel | undefined;

  getUniqueClassName(): string {
    return TAG;
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.channel = new MethodChannel(binding.getBinaryMessenger(), TAG)
    this.channel.setMethodCallHandler(this)
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.channel?.setMethodCallHandler(null);
    this.channel = undefined;
  }

  onMethodCall(call: Method<PERSON><PERSON>, result: MethodResult): void {
    logger.d("onMethodCall", `method: ${call.method}, arguments: ${JSON.stringify(call.args)}`);
    if (call.method === "appMonitor") {
      let params = JSON.parse(call.args);
      if (params) {
        this.appMonitor(params);
        result.success(null);
      } else {
        result.error("-1", "arguments is null", null);
      }
    } else {
      result.success(null);
    }
  }

  private appMonitor(args: object): void {
    const funcName: string = args["funcName"];
    const module: string = args["module"];
    const monitorPoint: string = args["monitorPoint"];
    const arg: string = args["arg"];
    if (arguments != null) {
      switch (funcName) {
        case "appMonitorRegister":
          const dimensions: string[] = args["dimensions"];
          const measures: string[] = args["measures"];
          AppMonitor.register(module, monitorPoint, measures, dimensions, false);
          break;
        case "appMonitorStatCommit":
          const dimensionMap: Map<string, string> = args["dimensionMap"];
          const measureMap: Map<string, string> = args["measureMap"];
          const dvs: DimensionValueSet = DimensionValueSet.create();

          dimensionMap.forEach((value: string, key: string) => {
            dvs.setValue(key, value);
          });

          if (measureMap != null && measureMap.size > 0) {
            const mvs: MeasureValueSet = MeasureValueSet.create();
            measureMap.forEach((value: string, key: string) => {
              mvs.setValue(key, value);
            });

            AppMonitor.Stat.commit(module, monitorPoint, dvs, mvs);
          } else {
            AppMonitor.Stat.commit(module, monitorPoint, dvs, null);
          }
          break;
        case "appMonitorAlarmCommitSuccess":
          if (arg) {
            AppMonitor.Alarm.commitSuccess(module, monitorPoint, arg);
          } else {
            AppMonitor.Alarm.commitSuccess(module, monitorPoint);
          }
          break;
        case "appMonitorAlarmCommitFail":
          const errorCode: string = args["errorCode"];
          const errorMsg: string = args["errorMsg"];
          if (arg) {
            AppMonitor.Alarm.commitFail(module, monitorPoint, errorCode, errorMsg, arg);
          } else {
            AppMonitor.Alarm.commitFail(module, monitorPoint, errorCode, errorMsg);
          }
          break;
        case "appMonitorCounterCommit":
          const value: number = args["value"];
          if (arg) {
            AppMonitor.Counter.commit(module, monitorPoint, value, arg);
          } else {
            AppMonitor.Counter.commit(module, monitorPoint, value);
          }
      }
    }
  }

}