import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

import { camelCase } from '@fliggy-ohos/commonutils';
import { Logger } from '@fliggy-ohos/logger';
import { ExHashMap } from '@fliggy-ohos/dynamicrouter';
import { login, LoginListener } from '@fliggy-ohos/login';
import { LoginNotificationTypes } from '@taobao-ohos/account';

const TAG: string = "FLoginPlugin";
const logger = new Logger(TAG);

let that : FLoginPlugin

export class FLoginPlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private context: Context | undefined;
  private channel: MethodChannel | undefined;
  private requestCode: number | undefined;
  private loginListener: LoginListener | null = null;

  constructor(context?: Context) {
    if (context) {
      this.context = context;
    }
    that = this
  }

  getUniqueClassName(): string {
    return 'FLoginPlugin'
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "fliggy_login");
    this.channel.setMethodCallHandler(this);
    this.loginListener = {
      onLoginSuccess:() => {
        that.channel?.invokeMethod("loginNotify", {
          'type': 'NOTIFY_LOGIN_SUCCESS',
          'requestCode': `${that.requestCode}`
        });
      },
      onLoginFailed:(errorCode: number, errorMessage: string) => {
        that.channel?.invokeMethod("loginNotify", {
          'type': 'NOTIFY_LOGIN_FAILED',
          'requestCode': `${that.requestCode}`
        });
      },
      onLogout:() => {
        that.channel?.invokeMethod("loginNotify", {
          'type': 'NOTIFY_LOGOUT',
          'requestCode': '-1'
        });
      },
      onError(errorCode: number, errorMessage: string) { // 取消登录回调这里
        that.channel?.invokeMethod("loginNotify", {
          'type': 'NOTIFY_LOGIN_CANCEL',
          'requestCode': `${that.requestCode}`
        });
      }
    }
    login.addListener(this.loginListener!);
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    if(this.loginListener) {
      login.removeListener(this.loginListener);
    }
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d(TAG, "--------onMethodCall -------" + call.method);
    const method: string = camelCase(call.method)
    try {
      const params = ExHashMap.parseJson(call.args);
      switch (method) {
        case 'login':
          if (!params || params.isEmpty()) {
            result.error("-1", "argument is null", null);
            return
          }
          this.requestCode = params.getInt("requestCode");
          if (!this.requestCode) {
            result.error("-2", "requestCode is null", null);
          }
          const showUI = params.getBool('showUI') ?? false
          login.login(showUI, undefined, this.requestCode).then((type) => {
            if (type === LoginNotificationTypes.Success) {
              result.success('success');
            } else if (type === LoginNotificationTypes.Cancel) {
              result.success('cancel');
            } else {
              result.success('fail');
            }
          }).catch((error:Object) => {
            result.error("-2", "login error:", null);
          });
          break;
        case 'logout':
          login.logout()
          result.success('true');
          break;
        case 'haslogin':
          const res = login.hasLogin()
          result.success(`${res}`);
          break;
        case 'getLoinInfoByType':
          this.getLoinInfoByType(params, result);
          break;
        default:
          result.notImplemented();
          break;
      }
    } catch (e) {
      result.error(e.message, e.stack, null);
    }
  }

  getLoinInfoByType(params: ExHashMap | undefined, result: MethodResult) {
    if (!login.hasLogin()) {
      result.error('-1', 'getLoginInfo must login first', null);
      return;
    }

    try {
      if (!params || params.isEmpty()) {
        result.error('-1', 'argument is null', null);
        return;
      }

      const type = params.getString('type');
      if (type) {
        result.error('-2', 'info type is null', null);
        return;
      }

      let response: string | null = null;
      switch (type) {
        case 'sid':
          response = login.getSid();
          break;
        case 'userNick':
          response = login.getUserNick();
          break;
        case 'displayNick':
          response = login.getDisplayNick();
          break;
        case 'headPicLink':
          response = login.getHeadPicLink();
          break;
        case 'loginToken':
          response = login.getLoginToken();
          break;
        case 'userEcode':
          response = login.getUserEcode();
          break;
        default:
          result.notImplemented();
          break;
      }

      if (response != null) {
        result.success(response);
      } else {
        result.error('-3', 'result is null', null);
      }
    } catch (error) {
      logger.e(TAG, error);
      result.error('-4', 'login info get error', null);
    }
  }
}
