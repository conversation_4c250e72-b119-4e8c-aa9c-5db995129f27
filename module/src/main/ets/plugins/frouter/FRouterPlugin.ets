import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  Method<PERSON>allHandler,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

import { Logger } from '@fliggy-ohos/logger';
import { navigator, NavigatorResult, Anim } from '@fliggy-ohos/router';
import { ExHashMap } from '@fliggy-ohos/dynamicrouter';
import { flutterPageMap } from '../../FlutterPageMap';

const TAG: string = "FRouterPlugin";
const logger = new Logger(TAG);
type FBridgeCall = (call: MethodCall, result: MethodResult) => void;

export class FRouterPlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private channel: MethodChannel | undefined;

  constructor() {
  }

  getUniqueClassName(): string {
    return 'FRouterPlugin'
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "fliggy_router");
    this.channel.setMethodCallHandler(this);
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d(TAG, "--------onMethodCall -------" + call.method);
    switch (call.method) {
      case "push":
        this.push(call, result);
        break;
      case "closeDialog":
        this.closeDialog(call, result);
        break;
      case "findPage":
        this.findPage(call, result);
        break;
      case "pop":
        this.pop(call, result);
        break;
      case "popTo":
        this.popTo(call, result);
        break;
      default:
        result.notImplemented();
        break;
    }
  }

  push(call: MethodCall, result: MethodResult) {
    let url: string = call.argument('url');
    let params = this.convertToRecord(call.argument('params'));
    let uniqueId:string = call.argument('__container_uniqueId_key__');
    let context = flutterPageMap.get(uniqueId)?.getPageContext();
    if(typeof params['anim'] === 'string') {
      params['_fli_anim_type'] = params['anim'];
    }
    navigator.openPage(context, {
      pageUrl: url,
      params: params
    }).then((navResult: NavigatorResult) => {
      result.success(navResult.params);
    }).catch((navResult: NavigatorResult) => {
      result.error(String(navResult.resultCode), JSON.stringify(navResult.params), "");
    });
  }

  pop(call: MethodCall, methodResult: MethodResult) {
    try {
      let uniqueId:string = call.argument('__container_uniqueId_key__');
      let result = this.convertToRecord(call.argument('result'));
      let exts = this.convertToRecord(call.argument('exts'));
      let context = flutterPageMap.get(uniqueId)?.getPageContext();
      let anim : Anim| undefined = undefined
      if(String(exts['animated']) === 'false') {
        anim = Anim.none;
      }
      navigator.popToBack(context, { params: result ,anim: anim});
    } catch (e) {
      methodResult.error("-1", e.message, null);
    }
  }

  popTo(call: MethodCall, methodResult: MethodResult) {
    try {
      let uniqueId:string = call.argument('__container_uniqueId_key__');
      let url:string = call.argument('url');
      let result = this.convertToRecord(call.argument('result'));
      let context = flutterPageMap.get(uniqueId)?.getPageContext();

      navigator.popToBack(context, { pageUrl: url, params: result });
    } catch (e) {
      methodResult.error("-1", e.message, null);
    }
  }

  closeDialog(call: MethodCall, result: MethodResult) {
    try {
      // const params = ExHashMap.parseJson(call.args);
      // const extsObject = params?.getHashMap('exts');
      //
      // if (extsObject && extsObject.hasKey('__container_uniqueId_key__')) {
      //   const uniqueId = extsObject.getString('__container_uniqueId_key__');

      // todo @钟易 待基础能力完善
      // const flutterPage = TripFlutterPageMap.getMap()[uniqueId];
      // if (flutterPage && typeof flutterPage === 'object') {
      //   const parentFragment = (flutterPage as any).parentFragment;
      //   if (parentFragment && typeof parentFragment === 'object') {
      //     const dialogFragment = parentFragment as DialogFragmentLike;
      //     if (typeof dialogFragment.dismiss === 'function') {
      //       dialogFragment.dismiss();
      //
      //       const activity = (flutterPage as any).activity;
      //       if (activity && typeof activity === 'object' && typeof (activity as OnCloseDialogFragmentLike).onCloseDialogFragment === 'function') {
      //         const resultObject = call.arguments['result'] || {};
      //         if ('_fli_request_code' in extsObject) {
      //           const requestCode = parseInt(extsObject['_fli_request_code'], 10);
      //
      //           if (!isNaN(requestCode)) {
      //             const data = new Intent();
      //             data.putExtraAll(ConvertUtils.convertMapToBundle(resultObject));
      //             (activity as OnCloseDialogFragmentLike).onCloseDialogFragment(requestCode, Activity.RESULT_OK, data);
      //           }
      //         }
      //       }
      //     }
      //   }
      // }
      // }

    } catch (e) {
      result.error(e.message, e.stack, null);
    }
  }

  findPage(call: MethodCall, result: MethodResult) {
    try {
      const params = ExHashMap.parseJson(call.args);
      let url: string = params?.getString('url') ?? ''
      let ret : boolean = navigator.findPage(url);
      result.success(ret);
    } catch (e) {
      result.error(e.message, e.stack, null);
    }
  }

  onGotoDataReset(args: object) {
    this.channel?.invokeMethod("__flutter_gotodata__", args);
  }

  convertToRecord(params: Map<string, Object> | object | undefined): Record<string, Object> {
    let obj: Record<string, Object> = {};
    if (params) {
      if (params instanceof Map) {
        params.forEach((value:Object, key:string) => {
          obj[key] = this.convertValue(value);
        });
      } else {
        let entries: [string, Object][] = Object.entries(params);
        for (const item of entries) {
          obj[item[0]] = this.convertValue(item[1]);
        }
      }
    }
    return obj;
  }

  convertValue(value: Object): Object {
    if (typeof value === "bigint") {  //int64 number整型取值范围
      if(Number.MIN_SAFE_INTEGER <= value && value <= Number.MAX_SAFE_INTEGER) {
        logger.e(TAG, "convertValue bigint 转 number："+String(value));
        return Number(value);
      } else {
        logger.e(TAG, "convertValue bigint 超出范围转为string："+String(value));
        return String(value);
      }
    } else if (value instanceof Map) {
      return this.convertToRecord(value);
    } else if (value instanceof Array) {
      let array: Array<Object> = [];
      value.forEach((item:Object) => {
        array.push(this.convertValue(item));
      });
      return array;
    }
    return value;
  }
}
