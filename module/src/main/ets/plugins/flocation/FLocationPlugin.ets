import {
  FlutterPlugin,
  FlutterPluginBinding
} from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import MethodChannel, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MethodResult
} from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';

import { camelCase } from '@fliggy-ohos/commonutils';
import { Logger } from "@fliggy-ohos/logger";
import locationManager from "@fliggy-ohos/location";

const TAG: string = "FLocationPlugin";
const logger = new Logger(TAG);

export class FLocationPlugin implements FlutterPlugin, MethodCallHandler {
  private pluginBinding: FlutterPluginBinding | undefined;
  private context: Context | undefined;
  private channel: MethodChannel | undefined;

  constructor(context?: Context) {
    if (context) {
      this.context = context;
    }
  }

  getUniqueClassName(): string {
    return 'FLocationPlugin'
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "fliggy_location");
    this.channel.setMethodCallHandler(this);
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    this.pluginBinding = binding;
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    logger.d(TAG, "--------onMethodCall -------" + call.method);
    const method: string = camelCase(call.method)
    try {
      switch (method) {
        case 'checkLocationPermission':
          locationManager.hasPermission().then((res) => {
            result.success({
              "result": res + ""
            });
          });
          break;
        case 'isLocationEnabled':
          const isEnabled = locationManager.isLocationEnabled();
          result.success({
            "result": isEnabled + ""
          });
          break;
        case 'getLocation':
          const location = locationManager.getLocation();
          if(!location){
            result.error("-1", "getLocation is null", null);
            return;
          }
          result.success({
            "result": JSON.stringify(location)
          });
          break;
        case 'request':
          locationManager.request().then((res) => {
            if(!res){
              result.error("-2", "request Location is null", null);
              return;
            }
            result.success({
              "result": JSON.stringify(res)
            });
          }).catch((err:object) => {
            result.error(err['errorCode'], err['errorInfo'], null);
          });
          break;
        case 'requestSpeculatedLocation':
          locationManager.request().then((res) => {
            if(!res){
              result.error("-2", "request Location is null", null);
              return;
            }
            result.success({
              "result": JSON.stringify(res)
            });
          }).catch((err:object) => {
            result.error(err['errorCode'], err['errorInfo'], null);
          });
          break;
        case 'requestLocationCoordinate2D':
          locationManager.request({onlyLatLng: true}).then((res) => {
            if(!res){
              result.error("-2", "request Location is null", null);
              return;
            }
            result.success({
              "result": JSON.stringify(res)
            });
          }).catch((err:object) => {
            result.error(err['errorCode'], err['errorInfo'], null);
          });
          break;
        default:
          result.notImplemented();
          break;
      }
    } catch (e) {
      result.error("-3", "location error", null);
    }
  }

}
