import { InitTask } from '@fliggy-ohos/launcher';
import { TripFlutterPage } from './pages/TripFlutterPage';
import { <PERSON><PERSON>, <PERSON>li<PERSON><PERSON><PERSON><PERSON>or, navigator, Navigator<PERSON><PERSON>ult, PageContext, RunningPageStack } from '@fliggy-ohos/router';
import {
  FlutterBoost,
  FlutterBoostDelegate,
  FlutterBoostRouteOptions,
  FlutterBoostSetupOptionsBuilder
} from '@fliggy-ohos/flutter_boost';
import { FlutterEngine, FlutterPlugin } from '@ohos/flutter_ohos';
import { logger } from './pages/FlutterPageController';
import emitter from '@ohos.events.emitter';
import { FbridgePlugin } from './plugins/fbridge/FbridgePlugin';
import { FPerformanceKitPlugin } from './plugins/ffperformance/FPerformanceKitPlugin';
import { FPlayerPlugin } from './plugins/fplayer/FPlayerPlugin';
import { FRouterPlugin } from './plugins/frouter/FRouterPlugin';
import { FLoginPlugin } from './plugins/flogin/FLoginPlugin';
import { FIconfontPlugin } from './plugins/ficonfont/FIconfontPlugin';
import { FLocationPlugin } from './plugins/flocation/FLocationPlugin';
import { EnvironUtils } from '@fliggy-ohos/appcompat';
import { FlutterWebviewPlugin } from './plugins/webview/FlutterWebviewPlugin';
import { PluginManager } from '@fliggy-ohos/unicorn';
import { pie } from '@taobao-ohos/pie';
import { FBroadcastPlugin } from './jsbridge/FBroadcastPlugin';
import { OnFlutterResult } from './jsbridge/OnFlutterResult';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { FlutterUtil } from './utils/FlutterUtil';
import { FTitlebarAdapterPlugin } from './plugins/ftitlebar_adapter/FTitlebarAdapterPlugin';
import { FliggyUsertrackPlugin } from './plugins/fliggy_usertrack/FliggyUsertrackPlugin';
import { FliggyStoragePlugin } from './plugins/fliggy_storage/FliggyStoragePlugin';

// entry中add进来
const pluginList : FlutterPlugin[] = [];

export function addPlugin(item: FlutterPlugin) {
  return pluginList.push(item);
}

export class InitFlutterTask implements InitTask, FlutterBoostDelegate {

  public execute(context: Context): void {
    this.registerPage();
    this.initFlutterEngine(context);
  }

  registerPage() {
    FliggyNavigator.registerBuilder("flutter_view", wrapBuilder(buildTripFlutterPage));

    PluginManager.register(['fbroadcast', 'fdispatch_event'], 0, true, () => { return new FBroadcastPlugin() });
    PluginManager.register('on_flutter_result', 0, true, () => { return new OnFlutterResult() });
    // 绑定到pie上，可通过pie.JSBind.callJSFunction调用，解决循环依赖问题
    pie.JSBind.bindFunction('FlutterUtil.fbroadcast',FlutterUtil.fbroadcast)
    pie.JSBind.bindFunction('FlutterUtil.fdispatchEvent',FlutterUtil.fdispatchEvent)
    pie.JSBind.bindFunction('FlutterUtil.fdispatchEventForResult',FlutterUtil.fdispatchEventForResult)
  }

  initFlutterEngine(context: Context) {
    addPlugin(new FbridgePlugin());
    addPlugin(new FRouterPlugin());
    addPlugin(new FPerformanceKitPlugin());
    addPlugin(new FPlayerPlugin());
    addPlugin(new FIconfontPlugin(context))
    addPlugin(new FLoginPlugin(context))
    addPlugin(new FLocationPlugin(context))
    addPlugin(new FlutterWebviewPlugin());
    addPlugin(new FTitlebarAdapterPlugin());
    addPlugin(new FliggyUsertrackPlugin());
    addPlugin(new FliggyStoragePlugin());

    AppStorage.setOrCreate('FlutterIsInitDone', false);
    let optionsBuilder = new FlutterBoostSetupOptionsBuilder();
    optionsBuilder.setDebugLoggingEnabled(EnvironUtils.debuggable());

    FlutterBoost.getInstance().setup(this, context, (engine: FlutterEngine) => {
      logger.d("InitFlutterTask", '引擎初始化成功');
      pluginList.forEach((plugin) => {
        engine.getPlugins()?.add(plugin);
      });
      AppStorage.setOrCreate('FlutterIsInitDone', true);
      let eventData: emitter.EventData = {
        data: {
          "FlutterIsInitDone": true,
        }
      };
      let options: emitter.Options = {
        priority: emitter.EventPriority.HIGH
      };
      emitter.emit('FlutterIsInitDone', options, eventData);
    }, optionsBuilder.build());
  }

  // 目前未有使用
  pushNativeRoute(options: FlutterBoostRouteOptions,  onPageResult?: (pageName: string, result: Record<string, Object>) => void): void {
    hilog.error(hilog.LogLevel.ERROR, 'InitFlutterTask', 'pushNativeRoute');
  }

  // 目前未有使用
  pushFlutterRoute(options: FlutterBoostRouteOptions, onPageResult?: (pageName: string, result: Record<string, Object>) => void): void {
    // throw new Error('Method not implemented.');
    hilog.error(hilog.LogLevel.ERROR, 'InitFlutterTask', 'pushFlutterRoute');
    // const urlParams: Record<string, Object> | undefined = options.getArguments();
    //
    // if (!isEmpty(options.getUniqueId()?.trim()) && urlParams) {
    //   urlParams["_fli_unique_id"] = options.getUniqueId()!;
    // }
    //
    // let anim: Anim = Anim.city_guide;
    // if (Object.keys(urlParams).includes('anim')) {
    //   const animStr = urlParams['anim'] as string;
    //   anim = Anim[animStr];
    // }
    //
    // const topActivity = RunningPageStack.getTopPageContext();
    //
    // navigator.openPage(topActivity, {
    //   pageUrl: options.getPageName(),
    //   anim: anim,
    //   params: urlParams
    // }).then((data: NavigatorResult) => {
    //   hilog.info(hilog.LogLevel.INFO, 'InitFlutterTask', 'pushFlutterRoute result success');
    //   if (data.resultCode === 0) {
    //     onPageResult?.(options.getPageName(), data.params);
    //   } else {
    //     onPageResult?.(options.getPageName(), {});
    //   }
    // }).catch((err:Object) => {
    //   hilog.info(hilog.LogLevel.INFO, 'InitFlutterTask', 'pushFlutterRoute result error');
    //   onPageResult?.(options.getPageName(), {});
    // });
  }

  popRoute(options: FlutterBoostRouteOptions): boolean {
    hilog.info(hilog.LogLevel.INFO, 'InitFlutterTask', 'popRoute');
    return false;
  }
}


@Builder
function buildTripFlutterPage(pageContext: PageContext) {
  TripFlutterPage({ pageContext: pageContext });
}
