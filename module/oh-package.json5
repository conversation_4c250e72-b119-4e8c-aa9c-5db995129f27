{
  name: '@fliggy-ohos/flutter_container',
  version: '1.0.1-SNAPSHOT.1748344801474',
  description: 'Please describe the basic information.',
  main: 'Index.ets',
  author: {
    name: '<PERSON>li<PERSON><PERSON><PERSON>ileTeam',
    email: '<EMAIL>',
  },
  license: 'Apache-2.0',
  dependencies: {
    '@taobao-ohos/pie': 'latest',
    '@fliggy-ohos/appcompat': 'latest',
    '@fliggy-ohos/commonui': 'latest',
    '@fliggy-ohos/commonutils': 'latest',
    '@fliggy-ohos/jsbridge': 'latest',
    '@fliggy-ohos/router': 'latest',
    '@fliggy-ohos/dynamicrouter': 'latest',
    '@taobao-ohos/ut_analytics_sdk': 'latest',
    '@fliggy-ohos/tracker': 'latest',
    '@fliggy-ohos/fliggykv': 'latest',
    '@fliggy-ohos/logger': 'latest',
    '@fliggy-ohos/titlebar': 'latest',
    '@fliggy-ohos/env': 'latest',
    '@fliggy-ohos/launcher': 'latest',
    '@fliggy-ohos/fperformancekit': 'latest',
    '@fliggy-ohos/player': 'latest',
    '@fliggy-ohos/permission': 'latest',
    '@fliggy-ohos/login': 'latest',
    '@fliggy-ohos/unicorn': 'latest',
    '@fliggy-ohos/location': 'latest',
    '@fliggy-ohos/configcenter': 'latest',
    '@taobao-ohos/account': '1.3.43',
    '@ohos/flutter_ohos': '1.0.0-fliggy-ohos-0d85c7350a-SNAPSHOT',
    '@fliggy-ohos/flutter_boost': '1.0.0-fccb14273a-SNAPSHOT',
    '@hadss/nodepool': '1.0.2-rc.2',
  },
  dynamicDependencies: {},
}