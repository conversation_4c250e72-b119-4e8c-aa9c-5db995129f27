{"app": {"signingConfigs": [], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "targetSdkVersion": "5.0.0(12)", "compatibleSdkVersionStage": "beta3", "buildOption": {"strictMode": {"useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "module", "srcPath": "./module"}]}