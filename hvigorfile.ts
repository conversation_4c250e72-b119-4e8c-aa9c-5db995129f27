import { appTasks } from '@ohos/hvigor-ohos-plugin';
import {mtl_dependencies_sync,mtl_hap_plugin_list} from '@ali/fliggy-mtl-protocol-parse'

function pluginsList() {
    // mtl构建
    console.log('pluginsList')
    if(process.env.MUPP_PROJECT_ID != undefined) {
        return mtl_hap_plugin_list();
    }
    // end mtl构建

    // 本地依赖同步，将integrateAreaId对应的value替换为你想要的变更单或者集成区的依赖，同步一次之后注释掉改代码，否则会每次sync都同步依赖
    // return [mtl_dependencies_sync({"integrateAreaId": 1151323})]
    return []
}

export default {
    system: appTasks,
    plugins: [...pluginsList()]
}